import { useState } from 'react';
import Home from './Home';
import Tryon from './Tryon';
import './App.css';

function App() {
  const [currentPage, setCurrentPage] = useState('home');

  const handleTryNow = () => {
    setCurrentPage('tryon');
  };

  const handleBackToHome = () => {
    setCurrentPage('home');
  };

  return (
    <div className="App">
      {currentPage === 'home' ? (
        <Home onTryNow={handleTryNow} />
      ) : (
        <Tryon onBackToHome={handleBackToHome} />
      )}
    </div>
  );
}

export default App;
