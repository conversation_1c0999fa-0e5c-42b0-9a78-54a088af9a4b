@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Inter font for better typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

body {
    background-color: #f8f9fa;
    color: #333;
    height: 100vh;
    overflow: hidden;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

#root {
    height: 100%;
}

/* Custom scrollbar styles for the new white and green theme */
.scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #2D8C88 #f7fafc;
}

/* Webkit scrollbar styles */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f7fafc;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #2D8C88;
    border-radius: 4px;
    border: 2px solid #f7fafc;
}

::-webkit-scrollbar-thumb:hover {
    background: #1f6b68;
}

/* Product selection panel specific scrollbar */
.product-scroll::-webkit-scrollbar {
    width: 6px;
}

.product-scroll::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 3px;
}

.product-scroll::-webkit-scrollbar-thumb {
    background: #2D8C88;
    border-radius: 3px;
}

.product-scroll::-webkit-scrollbar-thumb:hover {
    background: #1f6b68;
}

/* Mobile responsiveness improvements */
@media (max-width: 768px) {
    body {
        font-size: 14px;
    }

    .product-scroll {
        grid-template-columns: repeat(auto-fill, minmax(85px, 1fr)) !important;
        gap: 12px !important;
        max-height: 220px !important;
        padding: 10px !important;
    }

    /* Mobile-specific button adjustments */
    .mobile-btn {
        padding: 10px 14px !important;
        font-size: 12px !important;
        min-width: auto !important;
        height: 40px !important;
        border-radius: 20px !important;
    }

    .mobile-capture-btn {
        width: 70px !important;
        height: 70px !important;
        bottom: 20px !important;
        border-width: 3px !important;
    }

    .mobile-inner-circle {
        width: 50px !important;
        height: 50px !important;
    }

    .mobile-instruction {
        top: 15px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        padding: 10px 20px !important;
        font-size: 14px !important;
        max-width: 90% !important;
        text-align: center !important;
    }

    .mobile-product-panel {
        max-height: 50vh !important;
        padding: 20px 16px !important;
        border-radius: 20px 20px 0 0 !important;
    }

    .mobile-hand-guide {
        width: 90% !important;
        max-width: 350px !important;
    }
}

@media (max-width: 480px) {
    .product-scroll {
        grid-template-columns: repeat(auto-fill, minmax(75px, 1fr)) !important;
        gap: 8px !important;
        max-height: 200px !important;
    }

    .mobile-btn {
        padding: 8px 12px !important;
        font-size: 11px !important;
        height: 36px !important;
    }

    .mobile-capture-btn {
        width: 65px !important;
        height: 65px !important;
        bottom: 15px !important;
    }

    .mobile-inner-circle {
        width: 45px !important;
        height: 45px !important;
    }

    .mobile-instruction {
        padding: 8px 16px !important;
        font-size: 13px !important;
    }

    .mobile-product-panel {
        max-height: 45vh !important;
        padding: 16px 12px !important;
    }

    .mobile-hand-guide {
        width: 95% !important;
        max-width: 320px !important;
    }
}

/* Landscape mobile adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .mobile-capture-btn {
        bottom: 10px !important;
        width: 60px !important;
        height: 60px !important;
    }

    .mobile-inner-circle {
        width: 40px !important;
        height: 40px !important;
    }

    .mobile-instruction {
        top: 10px !important;
        padding: 6px 16px !important;
        font-size: 12px !important;
    }

    .mobile-btn {
        padding: 6px 10px !important;
        font-size: 10px !important;
        height: 32px !important;
    }

    .mobile-product-panel {
        max-height: 60vh !important;
    }
}

/* Ensure capture button is always visible on mobile */
@media (max-width: 768px) {
    .mobile-capture-btn {
        position: fixed !important;
        z-index: 1000 !important;
        bottom: 20px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }
}

/* Pulse animation for countdown */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Additional mobile viewport fixes */
@media (max-width: 768px) {
    body {
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
    }

    /* Prevent zoom on input focus */
    input, select, textarea {
        font-size: 16px !important;
    }

    /* Better touch targets */
    button, .mobile-btn {
        min-height: 44px !important;
        min-width: 44px !important;
    }

    /* Improve scrolling on mobile */
    .product-scroll {
        -webkit-overflow-scrolling: touch !important;
    }
}

/* --- Custom Switch Styles --- */
.switch {
  --switch-width: 46px;
  --switch-height: 24px;
  --switch-bg: #838383;
  --switch-checked-bg: #2D8C88;
  --switch-offset: calc((var(--switch-height) - var(--circle-diameter)) / 2);
  --switch-transition: all .2s cubic-bezier(0.27, 0.2, 0.25, 1.51);
  --circle-diameter: 18px;
  --circle-bg: #fff;
  --circle-shadow: 1px 1px 2px rgba(146, 146, 146, 0.45);
  --circle-checked-shadow: -1px 1px 2px rgba(163, 163, 163, 0.45);
  --circle-transition: var(--switch-transition);
  --icon-transition: all .2s cubic-bezier(0.27, 0.2, 0.25, 1.51);
  --icon-cross-color: var(--switch-bg);
  --icon-cross-size: 6px;
  --icon-checkmark-color: var(--switch-checked-bg);
  --icon-checkmark-size: 10px;
  --effect-width: calc(var(--circle-diameter) / 2);
  --effect-height: calc(var(--effect-width) / 2 - 1px);
  --effect-bg: var(--circle-bg);
  --effect-border-radius: 1px;
  --effect-transition: all .2s ease-in-out;
}
.switch input {
  display: none;
}
.switch {
  display: inline-block;
}
.switch svg {
  transition: var(--icon-transition);
  position: absolute;
  height: auto;
}
.switch .checkmark {
  width: var(--icon-checkmark-size);
  color: var(--icon-checkmark-color);
  transform: scale(0);
}
.switch .cross {
  width: var(--icon-cross-size);
  color: var(--icon-cross-color);
}
.slider {
  box-sizing: border-box;
  width: var(--switch-width);
  height: var(--switch-height);
  background: var(--switch-bg);
  border-radius: 999px;
  display: flex;
  align-items: center;
  position: relative;
  transition: var(--switch-transition);
  cursor: pointer;
}
.circle {
  width: var(--circle-diameter);
  height: var(--circle-diameter);
  background: var(--circle-bg);
  border-radius: inherit;
  box-shadow: var(--circle-shadow);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--circle-transition);
  z-index: 1;
  position: absolute;
  left: var(--switch-offset);
}
.slider::before {
  content: "";
  position: absolute;
  width: var(--effect-width);
  height: var(--effect-height);
  left: calc(var(--switch-offset) + (var(--effect-width) / 2));
  background: var(--effect-bg);
  border-radius: var(--effect-border-radius);
  transition: var(--effect-transition);
}
.switch input:checked+.slider {
  background: var(--switch-checked-bg);
}
.switch input:checked+.slider .checkmark {
  transform: scale(1);
}
.switch input:checked+.slider .cross {
  transform: scale(0);
}
.switch input:checked+.slider::before {
  left: calc(100% - var(--effect-width) - (var(--effect-width) / 2) - var(--switch-offset));
}
.switch input:checked+.slider .circle {
  left: calc(100% - var(--circle-diameter) - var(--switch-offset));
  box-shadow: var(--circle-checked-shadow);
}
@media (max-width: 600px) {
  .switch {
    --switch-width: 38px;
    --switch-height: 20px;
    --circle-diameter: 14px;
    font-size: 13px;
  }
}
